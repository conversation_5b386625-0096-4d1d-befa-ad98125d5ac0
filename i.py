import fitz  # PyMuPDF

# --- إعدادات ---
input_pdf = "template.pdf"               # اسم ملف PDF الأصلي
output_pdf = "pdf_with_fields.pdf"       # اسم الملف الناتج

# قائمة الـ placeholders التي تريد تحويلها إلى حقول نموذج
placeholders = [
    "serial_number", "t_11", "t_", "date", "location_1", "name_1",
    "location_2", "name_2", "phone_1", "id_1", "location_3", "name_3",
    "phone_2", "id_2", "sin_num", "car_prop", "car_city", "car_num",
    "car_colar", "car_model", "car_type", "sasi_num", "badal_writing", "badal_num",
    "mony_writing", "mony_num", "mony_not_delevired_writing", "mony_not_delevired",
    "note_a", "note_b", "day", "date_1", "t", "t_1",
    "seller_photo", "buyer_photo", "name_33", "name_22", "qr"
]

# --- فتح ملف PDF ---
doc = fitz.open(input_pdf)
page = doc[0]  # نبدأ من الصفحة الأولى فقط - يمكنك تعديلها لعدة صفحات

# --- إعدادات الرسم ---
font_size = 10
field_color = (0, 0, 1)  # أزرق
text_color = (0, 0, 0)   # أسود

# --- البحث والرسم ---
for name in placeholders:
    search_text = f"{{{{{name}}}}}"  # شكل النص المطلوب مثلاً {{name_1}}
    results = page.search_for(search_text)
    
    for rect in results:
        # إزالة النص الأصلي (placeholder)
        page.add_redact_annot(rect, fill=(1, 1, 1))  # أبيض
    page.apply_redactions()

for name in placeholders:
    search_text = f"{{{{{name}}}}}"
    results = page.search_for(search_text)

    for rect in results:
        # رسم مستطيل أزرق مكان الحقل
        field_rect = fitz.Rect(rect.x0, rect.y0, rect.x0 + 100, rect.y1 + 5)
        page.draw_rect(field_rect, color=field_color, width=0.5)

        # كتابة اسم الحقل داخل المستطيل
        page.insert_text((rect.x0 + 2, rect.y1), f"[{name}]", fontsize=font_size, color=text_color)

# --- حفظ الملف الناتج ---
doc.save(output_pdf)
print(f"✅ تم حفظ الملف الجديد باسم: {output_pdf}")
